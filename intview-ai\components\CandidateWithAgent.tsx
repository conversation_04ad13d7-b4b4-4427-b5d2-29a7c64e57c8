"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { Bot, Loader2 } from "lucide-react";

import { useInterview } from "@/context/InterviewContext";
import LiveStreamingAvatar from "./LiveStreamingAvatar";

type CandidateWithAgentProps = {
  className?: string;
  candidateName?: string;
  jobTitle?: string;
  useAgent?: boolean;
  message?: string;
  onVideoReady?: () => void;
  onVideoEnd?: () => void;
  useStreaming?: boolean;
  avatarMode?: "standard" | "streaming" | "live";
  // Live streaming specific props
  onConnectionReady?: () => void;
  onStreamReady?: () => void;
  onSpeechStart?: () => void;
  onSpeechEnd?: () => void;
  autoConnect?: boolean;
  greetingMessage?: string;
};

const CandidateWithAgent: React.FC<CandidateWithAgentProps> = ({
  className = "",
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
  useAgent = true,
  useStreaming = true,
  avatarMode = "live",
  onConnectionReady,
  onStreamReady,
  onSpeechStart,
  onSpeechEnd,
  autoConnect = true,
  greetingMessage,
}) => {
  const {
    agent,
    isCreatingAgent,
    agentError,
    createAgent,
    isLiveStreamingEnabled,
    setIsStreamConnected,
    setIsStreamReady,
  } = useInterview();

  const [isLiveMode, setIsLiveMode] = useState<boolean>(false);

  const instructions = `You are an AI interview assistant conducting an interview for the ${jobTitle} position with ${candidateName}. Be professional, engaging, and ask relevant questions about their experience and qualifications.`;
  const agentName = `${jobTitle} Interviewer`;

  // Determine if we should use live streaming mode
  useEffect(() => {
    const shouldUseLiveMode = isLiveStreamingEnabled && (useStreaming || avatarMode === "live");
    setIsLiveMode(shouldUseLiveMode);

    // If not using live mode, create traditional agent
    if (!shouldUseLiveMode && useAgent && !agent && !isCreatingAgent) {
      createAgent(instructions, agentName);
    }
  }, [useAgent, agent, isCreatingAgent, createAgent, instructions, agentName, isLiveStreamingEnabled, useStreaming, avatarMode]);

  // Handle live streaming events
  const handleConnectionReady = () => {
    setIsStreamConnected(true);
    onConnectionReady?.();
  };

  const handleStreamReady = () => {
    setIsStreamReady(true);
    onStreamReady?.();
  };

  const handleSpeechStart = () => {
    onSpeechStart?.();
  };

  const handleSpeechEnd = () => {
    onSpeechEnd?.();
  };

  return (
    <div className={`relative ${className}`}>
      {isLiveMode ? (
        // Live Streaming Avatar Mode
        <LiveStreamingAvatar
          className="w-full h-full"
          candidateName={candidateName}
          jobTitle={jobTitle}
          onConnectionReady={handleConnectionReady}
          onStreamReady={handleStreamReady}
          onSpeechStart={handleSpeechStart}
          onSpeechEnd={handleSpeechEnd}
          autoConnect={autoConnect}
          greetingMessage={greetingMessage}
        />
      ) : (
        // Traditional Static Agent Mode
        <div className="w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center overflow-hidden">
          {agent ? (
            <div className="text-center w-full h-full flex flex-col">
              {/* Avatar Image */}
              <div className="flex-1 flex items-center justify-center p-4">
                {agent.presenter?.thumbnail ? (
                  <Image
                    src={agent.presenter.thumbnail}
                    alt={agent.preview_name}
                    width={320}
                    height={550}
                    className="w-full h-full object-cover rounded-2xl shadow-lg max-w-xs max-h-80"
                    onError={(e) => {
                      console.error("Failed to load avatar image:", agent.presenter.thumbnail);
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                ) : (
                  <div className="w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center">
                    <Bot className="w-16 h-16 text-gray-600" />
                  </div>
                )}

                {/* Fallback icon (hidden by default, shown if image fails) */}
                <div className="hidden w-32 h-32 bg-gray-300 rounded-full items-center justify-center">
                  <Bot className="w-16 h-16 text-gray-600" />
                </div>
              </div>
            </div>
          ) : isCreatingAgent ? (
            <div className="text-center">
              <Loader2 className="w-12 h-12 animate-spin text-blue-500 mx-auto mb-4" />
              <p className="text-sm text-gray-600">
                {isLiveMode ? "Connecting to live avatar..." : "Creating AI Agent..."}
              </p>
              <p className="text-xs text-gray-500 mt-2">This may take a moment</p>
            </div>
          ) : agentError ? (
            <div className="text-center p-4">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Bot className="w-8 h-8 text-red-500" />
              </div>
              <p className="text-sm text-red-600 mb-2">Failed to create agent</p>
              <p className="text-xs text-gray-500">{agentError}</p>
              <button
                onClick={() => createAgent(instructions, agentName)}
                className="mt-3 px-4 py-2 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
              >
                Retry
              </button>
            </div>
          ) : (
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Bot className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-sm text-gray-600">
                {isLiveMode ? "Live avatar not available" : "No agent available"}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CandidateWithAgent;
