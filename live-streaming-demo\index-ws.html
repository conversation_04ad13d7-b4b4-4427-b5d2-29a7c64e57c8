<!DOCTYPE html>
<html>
  <head>
    <title>D-ID Streaming POC</title>
    <!-- added google fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Mulish:wght@300;400;700&display=swap" rel="stylesheet" />

    <style>
      .peerConnectionState-new {
        color: cornflowerblue;
      }
      .peerConnectionState-connecting {
        color: orange;
      }
      .peerConnectionState-connected {
        color: green;
      }
      .peerConnectionState-disconnected,
      .peerConnectionState-closed,
      .peerConnectionState-failed {
        color: red;
      }

      .iceConnectionState-new {
        color: cornflowerblue;
      }
      .iceConnectionState-checking {
        color: orange;
      }
      .iceConnectionState-connected,
      .iceConnectionState-completed {
        color: green;
      }
      .peerConnectionState-disconnected,
      .peerConnectionState-closed,
      .peerConnectionState-failed {
        color: red;
      }

      .iceGatheringState-new {
        color: cornflowerblue;
      }
      .iceGatheringState-gathering {
        color: orange;
      }
      .iceGatheringState-complete {
        color: black;
      }

      .signalingState-stable {
        color: green;
      }
      .signalingState-have-local-offer,
      .signalingState-have-remote-offer,
      .signalingState-have-local-pranswer,
      .signalingState-have-remote-pranswer {
        color: cornflowerblue;
      }
      .signalingState-closed {
        color: red;
      }

      .streamingState-streaming {
        color: green;
      }

      .streamingState-empty {
        color: grey;
      }

      .streamEvent-started {
        color: green;
      }

      .streamEvent-done {
        color: orange;
      }

      .streamEvent-ready {
        color: green;
      }

      .streamEvent-error {
        color: red;
      }

      .streamEvent-dont-care {
        color: gray;
      }

      /* added css from here */

      body * {
        font-family: 'Mulish', sans-serif;
        text-align: center;
      }

      #content {
        width: 820px;
        position: relative;
        margin: 0 auto;
      }

      #buttons {
        clear: both;
        padding: 0 0 0 0;
        text-align: center;
      }

      button {
        padding: 10px 20px;
        border-radius: 5px;
        border: none;
        font-size: 16px;
        margin: 0 5px;
        background-color: #7459fe;
        color: #fff;
      }

      button:hover {
        background-color: #9480ff;
        cursor: pointer;
        transition: all 0.2s ease-out;
      }

      #status {
        clear: both;
        padding: 20px 0 0 0;
        text-align: left;
        display: inline-block;
        zoom: 1;
        line-height: 140%;
        font-size: 15px;
      }

      #status div {
        padding-bottom: 10px;
      }

      #video-wrapper {
        background: url(bg.png);
        height: 500px;
        background-position: top;
      }

      #video-wrapper div {
        position: relative;
        height: 100%;
        width: 400px;
        margin: 0 auto;
      }

      video {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        /*border:1px solid;*/
        border-radius: 50%;
        background-color: #fff;
      }
    </style>
  </head>

  <body>
    <!-- adde "id=content" -->
    <div id="content">
      <!-- added "id=video-wrapper" -->
      <div id="video-wrapper">
        <div>
          <video id="idle-video-element" width="400" height="400" autoplay loop style="opacity: 1"></video>
          <video id="stream-video-element" width="400" height="400" autoplay style="opacity: 0"></video>
        </div>
      </div>
      <br />

      <!-- added div#buttons -->
      <div id="buttons">
        <button id="connect-button" type="button">Connect</button>
        <button id="stream-word-button" type="button">Stream word</button>
        <button id="stream-audio-button" type="button">Stream audio</button>
        <button id="destroy-button" type="button">Destroy</button>
      </div>

      <!-- added div#status -->
      <div id="status">
        <!-- removed the wrapping <div> tags -->
        ICE gathering status: <label id="ice-gathering-status-label"></label><br />
        ICE status: <label id="ice-status-label"></label><br />
        Peer connection status: <label id="peer-status-label"></label><br />
        Signaling status: <label id="signaling-status-label"></label><br />
        Last stream event: <label id="stream-event-label"></label><br />
        Streaming status: <label id="streaming-status-label"></label><br />
      </div>
    </div>

    <script type="module" src="./streaming-client-api-ws.js"></script>
  </body>
</html>
