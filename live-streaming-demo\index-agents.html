<!DOCTYPE html>
<html>

<head>
  <title>D-ID Agents API Demo</title>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Mulish:wght@300;400;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="style-agents.css">
  <link rel="icon" type="image/png" sizes="192x192" href="https://studio.d-id.com/favicon/favicon-192x192.png">
</head>

<body>
  <div id="content">

    <div id="status">
      <h4>Agent Status</h4>
      Agent ID: <label id="agentId-label"></label><br />
      Chat ID: <label id="chatId-label"></label><br />
      <br>
      <h4>WebRTC Connection Status</h4>
      ICE gathering status: <label id="ice-gathering-status-label"></label><br />
      ICE status: <label id="ice-status-label"></label><br />
      Peer connection status: <label id="peer-status-label"></label><br />
      Signaling status: <label id="signaling-status-label"></label><br />
      Streaming status: <label id="streaming-status-label"></label><br />
      <br>
      <div id="buttons">
        <button id="agents-button" type="button">Create new Agent with Knowledge</button>
        <br><br>
        <button id="connect-button" type="button">Connect</button>
        <button id="destroy-button" type="button">Destroy</button>
      </div>
    </div>

    <div id="video-wrapper">
      <div>
        <video id="video-element" width="400" height="400" src="" autoplay loop muted class="animated"></video>
      </div>
    </div>

    <div class="chat">
      <h4>Chat History</h4>
      <div id="msgHistory">
      </div>
    </div>

  </div>

  <div>
    <h3>Type your message here:</h3>
    <textarea id="textArea" cols="50" rows="5" maxlength="280"></textarea>
    <br>
    <button id="start-button" type="button">Send</button>
  </div>

  <script type="module" src="./agents-client-api.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</body>

</html>