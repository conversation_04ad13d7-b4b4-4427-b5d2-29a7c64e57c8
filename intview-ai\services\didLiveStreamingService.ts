// D-ID Live Streaming Service
// Based on the live-streaming-demo streaming-client-api.js

const DID_API_URL = "https://api.d-id.com";

interface DIDConfig {
  key: string;
  url: string;
  service: 'talks' | 'clips';
}

interface StreamSession {
  id: string;
  offer: RTCSessionDescriptionInit;
  ice_servers: RTCIceServer[];
  session_id: string;
}

interface ScriptConfig {
  type: 'text' | 'audio';
  provider?: {
    type: string;
    voice_id: string;
  };
  input?: string;
  audio_url?: string;
  ssml?: boolean;
}

export class DIDLiveStreamingService {
  private peerConnection: RTCPeerConnection | null = null;
  private pcDataChannel: RTCDataChannel | null = null;
  private streamId: string | null = null;
  private sessionId: string | null = null;
  private sessionClientAnswer: RTCSessionDescriptionInit | null = null;
  
  private statsIntervalId: number | null = null;
  private lastBytesReceived: number = 0;
  private videoIsPlaying: boolean = false;
  private streamVideoOpacity: number = 0;
  
  // Stream warmup to mitigate jittering issues
  private readonly streamWarmup: boolean = true;
  private isStreamReady: boolean = false;
  
  // Video elements
  private idleVideoElement: HTMLVideoElement | null = null;
  private streamVideoElement: HTMLVideoElement | null = null;
  
  // Event callbacks
  private onConnectionStateChange?: (state: string) => void;
  private onStreamReady?: () => void;
  private onStreamEvent?: (event: string, data?: any) => void;
  private onVideoStatusChange?: (isPlaying: boolean) => void;
  
  private config: DIDConfig;
  
  constructor(config: DIDConfig) {
    this.config = config;
    this.isStreamReady = !this.streamWarmup;
  }
  
  // Initialize video elements
  public initializeVideoElements(
    idleVideoElement: HTMLVideoElement,
    streamVideoElement: HTMLVideoElement
  ): void {
    this.idleVideoElement = idleVideoElement;
    this.streamVideoElement = streamVideoElement;
    
    // Set playsinline attribute for mobile compatibility
    this.idleVideoElement.setAttribute('playsinline', '');
    this.streamVideoElement.setAttribute('playsinline', '');
  }
  
  // Set event callbacks
  public setEventCallbacks(callbacks: {
    onConnectionStateChange?: (state: string) => void;
    onStreamReady?: () => void;
    onStreamEvent?: (event: string, data?: any) => void;
    onVideoStatusChange?: (isPlaying: boolean) => void;
  }): void {
    this.onConnectionStateChange = callbacks.onConnectionStateChange;
    this.onStreamReady = callbacks.onStreamReady;
    this.onStreamEvent = callbacks.onStreamEvent;
    this.onVideoStatusChange = callbacks.onVideoStatusChange;
  }
  
  // Connect to D-ID streaming service
  public async connect(presenterConfig?: any): Promise<boolean> {
    try {
      console.log("DIDLiveStreamingService: Starting connection...");

      if (this.peerConnection && this.peerConnection.connectionState === 'connected') {
        console.log("DIDLiveStreamingService: Already connected");
        return true;
      }

      this.stopAllStreams();
      this.closePC();

      // Default presenter configuration
      const defaultPresenterConfig = this.config.service === 'clips'
        ? {
            presenter_id: 'v2_public_alex@qcvo4gupoy',
            driver_id: 'e3nbserss8',
          }
        : {
            source_url: 'https://create-images-results.d-id.com/DefaultPresenters/Emma_f/v1_image.jpeg',
          };

      const presenterInput = presenterConfig || defaultPresenterConfig;
      console.log("DIDLiveStreamingService: Using presenter config:", presenterInput);
      
      // Create streaming session
      console.log("DIDLiveStreamingService: Creating streaming session...");
      const sessionResponse = await this.fetchWithRetries(`${this.config.url}/${this.config.service}/streams`, {
        method: 'POST',
        headers: {
          Authorization: `Basic ${this.config.key}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...presenterInput,
          stream_warmup: this.streamWarmup
        }),
      });

      console.log("DIDLiveStreamingService: Session response status:", sessionResponse.status);

      if (!sessionResponse.ok) {
        const errorText = await sessionResponse.text();
        console.error("DIDLiveStreamingService: Session creation failed:", errorText);
        throw new Error(`Failed to create stream session: ${sessionResponse.status} - ${errorText}`);
      }

      const sessionData: StreamSession = await sessionResponse.json();
      console.log("DIDLiveStreamingService: Session created:", sessionData);
      this.streamId = sessionData.id;
      this.sessionId = sessionData.session_id;
      
      // Create peer connection
      this.sessionClientAnswer = await this.createPeerConnection(
        sessionData.offer, 
        sessionData.ice_servers
      );
      
      // Submit SDP answer
      const sdpResponse = await fetch(`${this.config.url}/${this.config.service}/streams/${this.streamId}/sdp`, {
        method: 'POST',
        headers: {
          Authorization: `Basic ${this.config.key}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          answer: this.sessionClientAnswer,
          session_id: this.sessionId,
        }),
      });
      
      if (!sdpResponse.ok) {
        throw new Error(`Failed to submit SDP answer: ${sdpResponse.status}`);
      }
      
      return true;
    } catch (error) {
      console.error('Failed to connect to D-ID streaming service:', error);
      this.stopAllStreams();
      this.closePC();
      return false;
    }
  }
  
  // Speak text with the avatar
  public async speak(text: string, voiceId: string = 'en-US-AndrewNeural'): Promise<boolean> {
    if (!this.isConnectionReady()) {
      console.warn('Connection not ready for streaming');
      return false;
    }
    
    const script: ScriptConfig = {
      type: 'text',
      provider: { 
        type: 'microsoft', 
        voice_id: voiceId 
      },
      input: text,
      ssml: true,
    };
    
    try {
      const response = await this.fetchWithRetries(`${this.config.url}/${this.config.service}/streams/${this.streamId}`, {
        method: 'POST',
        headers: {
          Authorization: `Basic ${this.config.key}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          script,
          config: { stitch: true },
          session_id: this.sessionId,
          ...(this.config.service === 'clips' && {
            background: { color: '#FFFFFF' },
          }),
        }),
      });
      
      return response.ok;
    } catch (error) {
      console.error('Failed to send speech request:', error);
      return false;
    }
  }
  
  // Check if connection is ready for streaming
  private isConnectionReady(): boolean {
    return (
      (this.peerConnection?.signalingState === 'stable' || 
       this.peerConnection?.iceConnectionState === 'connected') &&
      this.isStreamReady
    );
  }
  
  // Destroy the streaming session
  public async destroy(): Promise<void> {
    if (this.streamId && this.sessionId) {
      try {
        await fetch(`${this.config.url}/${this.config.service}/streams/${this.streamId}`, {
          method: 'DELETE',
          headers: {
            Authorization: `Basic ${this.config.key}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ session_id: this.sessionId }),
        });
      } catch (error) {
        console.error('Failed to destroy stream session:', error);
      }
    }
    
    this.stopAllStreams();
    this.closePC();
  }
  
  // Get connection status
  public getConnectionStatus(): {
    peerConnectionState: string;
    iceConnectionState: string;
    signalingState: string;
    isStreamReady: boolean;
  } {
    return {
      peerConnectionState: this.peerConnection?.connectionState || 'new',
      iceConnectionState: this.peerConnection?.iceConnectionState || 'new',
      signalingState: this.peerConnection?.signalingState || 'stable',
      isStreamReady: this.isStreamReady,
    };
  }

  // Private methods for WebRTC management
  private async createPeerConnection(
    offer: RTCSessionDescriptionInit,
    iceServers: RTCIceServer[]
  ): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      this.peerConnection = new RTCPeerConnection({ iceServers });
      this.pcDataChannel = this.peerConnection.createDataChannel('JanusDataChannel');

      // Set up event listeners
      this.peerConnection.addEventListener('icegatheringstatechange', this.onIceGatheringStateChange.bind(this), true);
      this.peerConnection.addEventListener('icecandidate', this.onIceCandidate.bind(this), true);
      this.peerConnection.addEventListener('iceconnectionstatechange', this.onIceConnectionStateChange.bind(this), true);
      this.peerConnection.addEventListener('connectionstatechange', this.onPeerConnectionStateChange.bind(this), true);
      this.peerConnection.addEventListener('signalingstatechange', this.onSignalingStateChange.bind(this), true);
      this.peerConnection.addEventListener('track', this.onTrack.bind(this), true);
      this.pcDataChannel.addEventListener('message', this.onStreamEventMessage.bind(this), true);
    }

    await this.peerConnection.setRemoteDescription(offer);
    console.log('Set remote SDP OK');

    const sessionClientAnswer = await this.peerConnection.createAnswer();
    console.log('Create local SDP OK');

    await this.peerConnection.setLocalDescription(sessionClientAnswer);
    console.log('Set local SDP OK');

    return sessionClientAnswer;
  }

  private onIceGatheringStateChange(): void {
    if (this.peerConnection) {
      console.log('ICE gathering state:', this.peerConnection.iceGatheringState);
    }
  }

  private onIceCandidate(event: RTCPeerConnectionIceEvent): void {
    console.log('onIceCandidate', event);
    if (event.candidate) {
      const { candidate, sdpMid, sdpMLineIndex } = event.candidate;

      fetch(`${this.config.url}/${this.config.service}/streams/${this.streamId}/ice`, {
        method: 'POST',
        headers: {
          Authorization: `Basic ${this.config.key}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          candidate,
          sdpMid,
          sdpMLineIndex,
          session_id: this.sessionId,
        }),
      }).catch(error => console.error('Failed to send ICE candidate:', error));
    } else {
      // For the initial 2 sec idle stream at the beginning of the connection
      fetch(`${this.config.url}/${this.config.service}/streams/${this.streamId}/ice`, {
        method: 'POST',
        headers: {
          Authorization: `Basic ${this.config.key}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: this.sessionId,
        }),
      }).catch(error => console.error('Failed to send null ICE candidate:', error));
    }
  }

  private onIceConnectionStateChange(): void {
    if (this.peerConnection) {
      console.log('ICE connection state:', this.peerConnection.iceConnectionState);
      if (this.peerConnection.iceConnectionState === 'failed' ||
          this.peerConnection.iceConnectionState === 'closed') {
        this.stopAllStreams();
        this.closePC();
      }
    }
  }

  private onPeerConnectionStateChange(): void {
    if (this.peerConnection) {
      console.log('Peer connection state:', this.peerConnection.connectionState);
      this.onConnectionStateChange?.(this.peerConnection.connectionState);

      if (this.peerConnection.connectionState === 'connected') {
        this.playIdleVideo();

        // Fallback mechanism: if 'stream/ready' event isn't received within 5 seconds
        setTimeout(() => {
          if (!this.isStreamReady) {
            console.log('Forcing stream/ready');
            this.isStreamReady = true;
            this.onStreamReady?.();
          }
        }, 5000);
      }
    }
  }

  private onSignalingStateChange(): void {
    if (this.peerConnection) {
      console.log('Signaling state:', this.peerConnection.signalingState);
    }
  }

  private onTrack(event: RTCTrackEvent): void {
    if (!event.track) return;

    this.statsIntervalId = window.setInterval(async () => {
      if (this.peerConnection) {
        const stats = await this.peerConnection.getStats(event.track);
        stats.forEach((report) => {
          if (report.type === 'inbound-rtp' && report.kind === 'video') {
            const videoStatusChanged = this.videoIsPlaying !== (report.bytesReceived > this.lastBytesReceived);

            if (videoStatusChanged) {
              this.videoIsPlaying = report.bytesReceived > this.lastBytesReceived;
              this.handleVideoStatusChange(this.videoIsPlaying, event.streams[0]);
            }
            this.lastBytesReceived = report.bytesReceived;
          }
        });
      }
    }, 500);
  }

  private onStreamEventMessage(message: MessageEvent): void {
    if (this.pcDataChannel?.readyState === 'open') {
      const [event, _] = message.data.split(':');
      console.log('Stream event:', event);

      switch (event) {
        case 'stream/ready':
          setTimeout(() => {
            console.log('stream/ready received');
            this.isStreamReady = true;
            this.onStreamReady?.();
          }, 1000);
          break;
        case 'stream/started':
        case 'stream/done':
        case 'stream/error':
          this.onStreamEvent?.(event, message.data);
          break;
      }
    }
  }

  private handleVideoStatusChange(videoIsPlaying: boolean, stream?: MediaStream): void {
    let status: string;

    if (videoIsPlaying) {
      status = 'streaming';
      this.streamVideoOpacity = this.isStreamReady ? 1 : 0;
      this.setStreamVideoElement(stream);
    } else {
      status = 'empty';
      this.streamVideoOpacity = 0;
    }

    if (this.streamVideoElement) {
      this.streamVideoElement.style.opacity = this.streamVideoOpacity.toString();
    }
    if (this.idleVideoElement) {
      this.idleVideoElement.style.opacity = (1 - this.streamVideoOpacity).toString();
    }

    this.onVideoStatusChange?.(videoIsPlaying);
  }

  private setStreamVideoElement(stream?: MediaStream): void {
    if (!stream || !this.streamVideoElement) return;

    this.streamVideoElement.srcObject = stream;
    this.streamVideoElement.loop = false;
    this.streamVideoElement.muted = !this.isStreamReady;

    // Safari hotfix
    if (this.streamVideoElement.paused) {
      this.streamVideoElement
        .play()
        .then(() => {})
        .catch((e) => console.error('Failed to play stream video:', e));
    }
  }

  private playIdleVideo(): void {
    if (!this.idleVideoElement) return;

    const idleVideoSrc = this.config.service === 'clips'
      ? '/alex_v2_idle.mp4'
      : '/emma_idle.mp4';

    this.idleVideoElement.src = idleVideoSrc;
    this.idleVideoElement.loop = true;
  }

  private stopAllStreams(): void {
    if (this.streamVideoElement?.srcObject) {
      console.log('Stopping video streams');
      const tracks = (this.streamVideoElement.srcObject as MediaStream).getTracks();
      tracks.forEach((track) => track.stop());
      this.streamVideoElement.srcObject = null;
      this.streamVideoOpacity = 0;
    }
  }

  private closePC(): void {
    if (!this.peerConnection) return;

    console.log('Stopping peer connection');
    this.peerConnection.close();

    // Remove event listeners
    this.peerConnection.removeEventListener('icegatheringstatechange', this.onIceGatheringStateChange, true);
    this.peerConnection.removeEventListener('icecandidate', this.onIceCandidate, true);
    this.peerConnection.removeEventListener('iceconnectionstatechange', this.onIceConnectionStateChange, true);
    this.peerConnection.removeEventListener('connectionstatechange', this.onPeerConnectionStateChange, true);
    this.peerConnection.removeEventListener('signalingstatechange', this.onSignalingStateChange, true);
    this.peerConnection.removeEventListener('track', this.onTrack, true);

    if (this.statsIntervalId) {
      clearInterval(this.statsIntervalId);
      this.statsIntervalId = null;
    }

    this.isStreamReady = !this.streamWarmup;
    this.streamVideoOpacity = 0;
    this.peerConnection = null;
    this.pcDataChannel = null;
    this.streamId = null;
    this.sessionId = null;
    this.sessionClientAnswer = null;

    console.log('Stopped peer connection');
  }

  // Utility method for retrying fetch requests
  private async fetchWithRetries(
    url: string,
    options: RequestInit,
    retries: number = 1,
    maxRetries: number = 3,
    maxDelaySec: number = 4
  ): Promise<Response> {
    try {
      return await fetch(url, options);
    } catch (err) {
      if (retries <= maxRetries) {
        const delay = Math.min(Math.pow(2, retries) / 4 + Math.random(), maxDelaySec) * 1000;

        await new Promise((resolve) => setTimeout(resolve, delay));

        console.log(`Request failed, retrying ${retries}/${maxRetries}. Error: ${err}`);
        return this.fetchWithRetries(url, options, retries + 1, maxRetries, maxDelaySec);
      } else {
        throw new Error(`Max retries exceeded. Error: ${err}`);
      }
    }
  }
}
