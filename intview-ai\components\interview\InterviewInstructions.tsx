"use client";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import React, { useState } from "react";

type InterviewInstructionsProps = {
  candidateName?: string;
  jobTitle?: string;
  languages?: string[];
  instructions?: string[];
  environmentChecklist?: string[];
  disclaimers?: string[];
  onNext?: () => void;
};

const defaultInstructions = [
  "The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.",
  "The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.",
  "The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.",
  "The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.",
  "The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.",
];

const defaultEnvironment = [
  "To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.",
  "To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.",
  "To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.",
  "To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.",
];

const defaultDisclaimers = [
  "Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .",
  "AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .",
  "Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .",
  "Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer .",
];

const InterviewInstructions: React.FC<InterviewInstructionsProps> = ({
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
  languages = ["English", "Chinese"],
  instructions = defaultInstructions,
  environmentChecklist = defaultEnvironment,
  disclaimers = defaultDisclaimers,
  onNext,
}) => {
  const [isChecked, setIsChecked] = useState(false);

  return (
    <div className="flex-1 border border-gray-400 rounded-md h-fit bg-white">
      <div className="p-4 flex flex-col text-[#38383a]">
        <p className="font-semibold mb-8 text-xl">
          Instructions for Interview!
        </p>
        <div className="space-y-6">
          <div>
            <p className=" mb-2 text-md">Hello {candidateName}!</p>
            <p className="text-sm mb-4">
              As part of the process you are required to complete an AI video
              assessment for the role of the {jobTitle}.
            </p>
          </div>

          <div>
            <p className="font-semibold mb-2 text-lg">Interview Language</p>
            <ul className="list-disc list-inside space-y-2 text-sm">
              {languages.map((language, index) => (
                <li key={index}>{language}</li>
              ))}
            </ul>
          </div>

          <div>
            <p className="font-semibold mb-2 text-lg">Instructions</p>
            <ul className="list-disc list-inside space-y-2 text-sm">
              {instructions.map((instruction, index) => (
                <li key={index}>{instruction}</li>
              ))}
            </ul>
          </div>

          <div>
            <p className="font-semibold mb-2 text-lg">Environment Checklist:</p>
            <ul className="list-disc list-inside space-y-2 text-sm">
              {environmentChecklist.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>

          <div>
            <p className="font-semibold mb-2 text-lg">Important Disclaimers:</p>
            <ul className="list-disc list-inside space-y-2 text-sm">
              {disclaimers.map((disclaimer, index) => (
                <li key={index}>{disclaimer}</li>
              ))}
            </ul>
          </div>

          <div className="flex items-start gap-2 mt-6">
            <input
              type="checkbox"
              id="terms"
              checked={isChecked}
              onChange={(e) => setIsChecked(e.target.checked)}
              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label htmlFor="terms" className="text-[11px] text-[#38383a]">
              By checking this box, you agree with AI Interview{" "}
              <span className="text-primary cursor-pointer font-medium">
                Terms of use
              </span>
              .
            </label>
          </div>
          <div className="flex justify-center">
            <Button
              disabled={!isChecked}
              variant="default"
              size="lg"
              className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
              onClick={() => onNext && onNext()}
            >
            Proceed
              <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InterviewInstructions;
