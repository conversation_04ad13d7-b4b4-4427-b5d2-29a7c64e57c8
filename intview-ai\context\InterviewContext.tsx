"use client";
import React, { createContext, useContext, useState, useCallback, ReactNode } from "react";

const DID_API_URL = "https://api.d-id.com";

interface Agent {
  id: string;
  preview_name: string;
  status: string;
  presenter: {
    type: string;
    voice: {
      type: string;
      voice_id: string;
    };
    thumbnail: string;
    source_url: string;
  };
  llm: {
    type: string;
    provider: string;
    model: string;
    instructions: string;
  };
}

interface InterviewContextType {
  // D-ID Agent state
  agent: Agent | null;
  isCreatingAgent: boolean;
  agentError: string | null;
  createAgent: (instructions: string, agentName: string) => Promise<void>;

  // Live Streaming state
  isLiveStreamingEnabled: boolean;
  setIsLiveStreamingEnabled: (enabled: boolean) => void;
  isStreamConnected: boolean;
  setIsStreamConnected: (connected: boolean) => void;
  isStreamReady: boolean;
  setIsStreamReady: (ready: boolean) => void;

  // Interview state
  currentQuestion: number;
  setCurrentQuestion: (question: number) => void;
  isInterviewStarted: boolean;
  setIsInterviewStarted: (started: boolean) => void;

  // Questions data
  questions: string[];

  // Live streaming methods
  speakQuestion: (questionIndex: number) => Promise<boolean>;
  speakGreeting: (candidateName: string, jobTitle: string) => Promise<boolean>;
}

const InterviewContext = createContext<InterviewContextType | undefined>(undefined);

interface InterviewProviderProps {
  children: ReactNode;
}

export const InterviewProvider: React.FC<InterviewProviderProps> = ({ children }) => {
  // D-ID Agent state
  const [agent, setAgent] = useState<Agent | null>(null);
  const [isCreatingAgent, setIsCreatingAgent] = useState<boolean>(false);
  const [agentError, setAgentError] = useState<string | null>(null);

  // Live Streaming state
  const [isLiveStreamingEnabled, setIsLiveStreamingEnabled] = useState<boolean>(true);
  const [isStreamConnected, setIsStreamConnected] = useState<boolean>(false);
  const [isStreamReady, setIsStreamReady] = useState<boolean>(false);

  // Interview state
  const [currentQuestion, setCurrentQuestion] = useState<number>(1);
  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);

  // Questions data
  const questions = [
    "Tell us about yourself and your background?",
    "What are your key strengths and how do they relate to this position?",
    "Why are you interested in this job and our company?",
    "Where do you see yourself in 5 years, and how does this role fit into your career goals?",
  ];

  const getAuthHeaders = () => {
    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || "";
    console.log("Using D-ID API Key:", apiKey ? `${apiKey.substring(0, 10)}...` : "NOT_FOUND");

    return {
      "Authorization": `Basic ${apiKey}`,
      "Content-Type": "application/json",
    };
  };

  const createAgent = useCallback(async (instructions: string, agentName: string) => {
    // If agent already exists with same instructions, don't recreate
    if (agent && agent.llm.instructions === instructions && agent.preview_name === agentName) {
      return;
    }

    setIsCreatingAgent(true);
    setAgentError(null);

    const payload = {
      presenter: {
        type: "talk",
        voice: {
          type: "microsoft",
          voice_id: "en-US-JennyMultilingualV2Neural"
        },
        thumbnail: "https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg",
        source_url: "https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg"
      },
      llm: {
        type: "openai",
        provider: "openai",
        model: "gpt-4o-mini",
        instructions: instructions
      },
      preview_name: agentName
    };

    try {
      console.log("Creating D-ID Agent with payload:", payload);

      const response = await fetch(`${DID_API_URL}/agents`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(payload),
      });

      console.log("D-ID Agent API Response Status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("D-ID Agent API Error Response:", errorText);
        throw new Error(`Failed to create agent: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const agentData: Agent = await response.json();
      console.log("D-ID Agent Created Successfully:", agentData);
      setAgent(agentData);
    } catch (err: unknown) {
      console.error("D-ID Agent Creation Error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to create agent";
      setAgentError(`Agent Creation Failed: ${errorMessage}`);
    } finally {
      setIsCreatingAgent(false);
    }
  }, [agent]);

  // Live streaming methods
  const speakQuestion = useCallback(async (questionIndex: number): Promise<boolean> => {
    if (!isLiveStreamingEnabled || !isStreamReady) {
      console.warn('Live streaming not ready');
      return false;
    }

    if (questionIndex < 0 || questionIndex >= questions.length) {
      console.error('Invalid question index:', questionIndex);
      return false;
    }

    const questionText = questions[questionIndex];
    const enhancedQuestion = `Question ${questionIndex + 1}: ${questionText} Please take your time to think about your answer.`;

    try {
      // Access the global live streaming avatar instance
      const avatarInstance = (window as any).liveStreamingAvatar;
      if (avatarInstance && avatarInstance.speak) {
        return await avatarInstance.speak(enhancedQuestion);
      }
      return false;
    } catch (error) {
      console.error('Failed to speak question:', error);
      return false;
    }
  }, [isLiveStreamingEnabled, isStreamReady, questions]);

  const speakGreeting = useCallback(async (candidateName: string, jobTitle: string): Promise<boolean> => {
    if (!isLiveStreamingEnabled || !isStreamReady) {
      console.warn('Live streaming not ready for greeting');
      return false;
    }

    const greetingText = `Hello ${candidateName}! Welcome to your interview for the ${jobTitle} position. I'm your AI interviewer, and I'll be asking you a few questions today. Are you ready to begin?`;

    try {
      // Access the global live streaming avatar instance
      const avatarInstance = (window as any).liveStreamingAvatar;
      if (avatarInstance && avatarInstance.speak) {
        return await avatarInstance.speak(greetingText);
      }
      return false;
    } catch (error) {
      console.error('Failed to speak greeting:', error);
      return false;
    }
  }, [isLiveStreamingEnabled, isStreamReady]);

  const value: InterviewContextType = {
    // D-ID Agent state
    agent,
    isCreatingAgent,
    agentError,
    createAgent,

    // Live Streaming state
    isLiveStreamingEnabled,
    setIsLiveStreamingEnabled,
    isStreamConnected,
    setIsStreamConnected,
    isStreamReady,
    setIsStreamReady,

    // Interview state
    currentQuestion,
    setCurrentQuestion,
    isInterviewStarted,
    setIsInterviewStarted,

    // Questions data
    questions,

    // Live streaming methods
    speakQuestion,
    speakGreeting,
  };

  return (
    <InterviewContext.Provider value={value}>
      {children}
    </InterviewContext.Provider>
  );
};

export const useInterview = (): InterviewContextType => {
  const context = useContext(InterviewContext);
  if (context === undefined) {
    throw new Error('useInterview must be used within an InterviewProvider');
  }
  return context;
};
