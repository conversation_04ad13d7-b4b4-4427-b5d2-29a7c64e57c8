{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_5399b416._.js", "server/edge/chunks/node_modules_@auth_core_5ebafa38._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_e184ff1b._.js", "server/edge/chunks/[root-of-the-server]__df53d061._.js", "server/edge/chunks/edge-wrapper_3d09a47d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "EgkodQbEY9m1pIgxkdt6YkPs3VngzrM6PXdSaRub/rI=", "__NEXT_PREVIEW_MODE_ID": "368faefc03e87c9f20684a31e228002d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e288eb6c4a8b40eff8959d90bfd1a5b84ccd1b91a065fee9b8d307444c844a50", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "42eb4ee5d7b2e718f32c0d5adb2cd93aaa7e2bae1df030a596a1b071b1d1c61e"}}}, "sortedMiddleware": ["/"], "functions": {}}