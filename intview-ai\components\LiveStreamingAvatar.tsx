"use client";
import React, { useEffect, useRef, useState, useCallback } from "react";
import { Bo<PERSON>, Loader2, Wifi, WifiOff } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { DIDLiveStreamingService } from "@/services/didLiveStreamingService";

interface LiveStreamingAvatarProps {
  className?: string;
  candidateName?: string;
  jobTitle?: string;
  onConnectionReady?: () => void;
  onStreamReady?: () => void;
  onSpeechStart?: () => void;
  onSpeechEnd?: () => void;
  autoConnect?: boolean;
  greetingMessage?: string;
}

interface ConnectionStatus {
  peerConnectionState: string;
  iceConnectionState: string;
  signalingState: string;
  isStreamReady: boolean;
}

const LiveStreamingAvatar: React.FC<LiveStreamingAvatarProps> = ({
  className = "",
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
  onConnectionReady,
  onStreamReady,
  onSpeechStart,
  onSpeechEnd,
  autoConnect = true,
  greetingMessage,
}) => {
  const idleVideoRef = useRef<HTMLVideoElement>(null);
  const streamVideoRef = useRef<HTMLVideoElement>(null);
  const streamingServiceRef = useRef<DIDLiveStreamingService | null>(null);
  
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    peerConnectionState: 'new',
    iceConnectionState: 'new',
    signalingState: 'stable',
    isStreamReady: false,
  });
  const [error, setError] = useState<string | null>(null);
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);

  // Initialize the streaming service
  const initializeStreamingService = useCallback(() => {
    // Try to get API key from multiple sources
    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY ||
                   process.env.DID_API_KEY ||
                   "******************************:rMMu3KcLvThOQcPwNtrcl"; // Fallback from api.json

    console.log("Initializing D-ID streaming service with API key:", apiKey ? `${apiKey.substring(0, 10)}...` : "NOT_FOUND");

    if (!apiKey) {
      setError("D-ID API key not found");
      return null;
    }

    const config = {
      key: apiKey,
      url: "https://api.d-id.com",
      service: "clips" as const, // Use clips for high-quality avatars
    };

    const service = new DIDLiveStreamingService(config);
    
    // Set up event callbacks
    service.setEventCallbacks({
      onConnectionStateChange: (state: string) => {
        console.log("Connection state changed:", state);
        setConnectionStatus(prev => ({ ...prev, peerConnectionState: state }));
        
        if (state === 'connected') {
          setIsConnected(true);
          setIsConnecting(false);
          onConnectionReady?.();
        } else if (state === 'failed' || state === 'closed') {
          setIsConnected(false);
          setIsConnecting(false);
          setError("Connection failed");
        }
      },
      onStreamReady: () => {
        console.log("Stream ready");
        setConnectionStatus(prev => ({ ...prev, isStreamReady: true }));
        onStreamReady?.();
        
        // Speak greeting message if provided
        if (greetingMessage && streamingServiceRef.current) {
          console.log("LiveStreamingAvatar: Scheduling greeting message:", greetingMessage);
          setTimeout(() => {
            console.log("LiveStreamingAvatar: Speaking greeting message");
            speakText(greetingMessage);
          }, 2000); // Increased delay to ensure connection is fully ready
        }
      },
      onStreamEvent: (event: string, data?: any) => {
        console.log("Stream event:", event, data);
        
        if (event === 'stream/started') {
          setIsSpeaking(true);
          onSpeechStart?.();
        } else if (event === 'stream/done') {
          setIsSpeaking(false);
          onSpeechEnd?.();
        }
      },
      onVideoStatusChange: (isPlaying: boolean) => {
        console.log("Video status changed:", isPlaying);
      },
    });

    return service;
  }, [onConnectionReady, onStreamReady, onSpeechStart, onSpeechEnd, greetingMessage]);

  // Connect to the streaming service
  const connect = useCallback(async () => {
    if (isConnecting || isConnected) {
      console.log("Already connecting or connected, skipping...");
      return;
    }

    console.log("Starting connection to D-ID streaming service...");
    setIsConnecting(true);
    setError(null);

    try {
      const service = streamingServiceRef.current || initializeStreamingService();
      if (!service) {
        throw new Error("Failed to initialize streaming service");
      }

      streamingServiceRef.current = service;
      console.log("Streaming service initialized");

      // Wait for video elements to be available
      if (!idleVideoRef.current || !streamVideoRef.current) {
        console.log("Waiting for video elements...");
        // Wait a bit for the DOM to be ready
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Initialize video elements
      if (idleVideoRef.current && streamVideoRef.current) {
        console.log("Initializing video elements");
        service.initializeVideoElements(idleVideoRef.current, streamVideoRef.current);
      } else {
        console.warn("Video elements not found");
      }

      // Connect to D-ID streaming service
      console.log("Connecting to D-ID streaming service...");
      const success = await service.connect();

      if (!success) {
        throw new Error("Failed to connect to streaming service");
      }

      console.log("Successfully connected to D-ID streaming service");

    } catch (err) {
      console.error("Connection error:", err);
      setError(err instanceof Error ? err.message : "Connection failed");
      setIsConnecting(false);
    }
  }, [isConnecting, isConnected, initializeStreamingService]);

  // Speak text using the avatar
  const speakText = useCallback(async (text: string, voiceId?: string) => {
    if (!streamingServiceRef.current || !connectionStatus.isStreamReady) {
      console.warn("Cannot speak: streaming service not ready");
      return false;
    }
    
    try {
      const success = await streamingServiceRef.current.speak(text, voiceId);
      return success;
    } catch (error) {
      console.error("Failed to speak text:", error);
      return false;
    }
  }, [connectionStatus.isStreamReady]);

  // Disconnect from the streaming service
  const disconnect = useCallback(async () => {
    if (streamingServiceRef.current) {
      await streamingServiceRef.current.destroy();
      streamingServiceRef.current = null;
    }
    
    setIsConnected(false);
    setIsConnecting(false);
    setConnectionStatus({
      peerConnectionState: 'new',
      iceConnectionState: 'new',
      signalingState: 'stable',
      isStreamReady: false,
    });
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }
    
    // Cleanup on unmount
    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // Expose methods to parent component
  useEffect(() => {
    // You can expose these methods to parent components if needed
    (window as any).liveStreamingAvatar = {
      speak: speakText,
      connect,
      disconnect,
      getStatus: () => connectionStatus,
    };
  }, [speakText, connect, disconnect, connectionStatus]);

  const getConnectionStatusColor = () => {
    if (isConnected && connectionStatus.isStreamReady) return "text-green-500";
    if (isConnecting) return "text-yellow-500";
    if (error) return "text-red-500";
    return "text-gray-500";
  };

  const getConnectionStatusIcon = () => {
    if (isConnected && connectionStatus.isStreamReady) return <Wifi className="w-4 h-4" />;
    if (isConnecting) return <Loader2 className="w-4 h-4 animate-spin" />;
    return <WifiOff className="w-4 h-4" />;
  };

  return (
    <div className={`relative ${className}`}>
      <div className="w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center overflow-hidden">
        
        {/* Connection Status Indicator */}
        <div className="absolute top-4 right-4 z-10">
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full bg-white/80 backdrop-blur-sm ${getConnectionStatusColor()}`}>
            {getConnectionStatusIcon()}
            <span className="text-xs font-medium">
              {isConnected && connectionStatus.isStreamReady ? "Live" : 
               isConnecting ? "Connecting..." : 
               error ? "Error" : "Offline"}
            </span>
          </div>
        </div>

        {/* Speaking Indicator */}
        <AnimatePresence>
          {isSpeaking && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="absolute top-4 left-4 z-10"
            >
              <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-green-500/80 backdrop-blur-sm text-white">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                <span className="text-xs font-medium">Speaking</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Video Container */}
        <div className="relative w-full h-full flex items-center justify-center">
          {/* Idle Video (shown when not streaming) */}
          <video
            ref={idleVideoRef}
            className="absolute inset-0 w-full h-full object-cover rounded-2xl"
            style={{ opacity: 1 }}
            autoPlay
            loop
            muted
            playsInline
          />
          
          {/* Stream Video (shown when streaming) */}
          <video
            ref={streamVideoRef}
            className="absolute inset-0 w-full h-full object-cover rounded-2xl"
            style={{ opacity: 0 }}
            autoPlay
            playsInline
          />
          
          {/* Fallback content when not connected */}
          {!isConnected && !isConnecting && (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-100 rounded-2xl">
              <Bot className="w-16 h-16 text-gray-400 mb-4" />
              <p className="text-gray-600 text-center px-4">
                {error ? `Error: ${error}` : "Avatar not connected"}
              </p>
              {!autoConnect && (
                <button
                  onClick={connect}
                  className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  Connect Avatar
                </button>
              )}
            </div>
          )}
          
          {/* Loading overlay */}
          {isConnecting && (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/20 rounded-2xl">
              <Loader2 className="w-8 h-8 text-white animate-spin mb-2" />
              <p className="text-white text-sm">Connecting to avatar...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LiveStreamingAvatar;
