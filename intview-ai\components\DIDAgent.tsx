"use client";
import React, { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import { Bot } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

const DID_API_URL = "https://api.d-id.com";

interface DIDAgentProps {
  className?: string;
  instructions?: string;
  agentName?: string;
}

interface Agent {
  id: string;
  preview_name: string;
  status: string;
  presenter: {
    type: string;
    voice: {
      type: string;
      voice_id: string;
    };
    thumbnail: string;
    source_url: string;
  };
  llm: {
    type: string;
    provider: string;
    model: string;
    instructions: string;
  };
}

const DIDAgent: React.FC<DIDAgentProps> = ({
  className = "",
  instructions = "You are an AI interview assistant designed to conduct professional interviews.",
  agentName = "Interview Assistant",
}) => {
  const [agent, setAgent] = useState<Agent | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isCreatingAgent, setIsCreatingAgent] = useState<boolean>(false);

  const getAuthHeaders = () => {
    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || "";
    console.log("Using D-ID API Key:", apiKey ? `${apiKey.substring(0, 10)}...` : "NOT_FOUND");

    return {
      "Authorization": `Basic ${apiKey}`,
      "Content-Type": "application/json",
    };
  };

  const createAgent = useCallback(async () => {
    setIsCreatingAgent(true);
    setError(null);

    const payload = {
      presenter: {
        type: "talk",
        voice: {
          type: "microsoft",
          voice_id: "en-US-JennyMultilingualV2Neural"
        },
        thumbnail: "https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg",
        source_url: "https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg"
      },
      llm: {
        type: "openai",
        provider: "openai",
        model: "gpt-4o-mini",
        instructions
      },
      preview_name: agentName
    };

    try {
      console.log("Creating D-ID Agent with payload:", payload);

      const response = await fetch(`${DID_API_URL}/agents`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(payload),
      });

      console.log("D-ID Agent API Response Status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("D-ID Agent API Error Response:", errorText);
        throw new Error(`Failed to create agent: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const agentData: Agent = await response.json();
      console.log("D-ID Agent Created Successfully:", agentData);
      console.log("Agent Presenter Data:", agentData.presenter);
      console.log("Avatar Thumbnail URL:", agentData.presenter?.thumbnail);
      setAgent(agentData);
    } catch (err: unknown) {
      console.error("D-ID Agent Creation Error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to create agent";
      setError(`Agent Creation Failed: ${errorMessage}`);

      // Log additional context for debugging
      console.log("D-ID API URL:", `${DID_API_URL}/agents`);
      console.log("Request payload:", JSON.stringify(payload, null, 2));
    } finally {
      setIsCreatingAgent(false);
    }
  }, [instructions, agentName]);

  // Initialize agent on component mount
  useEffect(() => {
    if (!agent && !isCreatingAgent) {
      createAgent();
    }
  }, [agent, isCreatingAgent, createAgent]);

  return (
    <div className={`relative ${className}`}>
      <AnimatePresence>
        {isCreatingAgent && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-gray-100 rounded-lg flex flex-col items-center justify-center z-10"
          >
            {/* <Loader2 className="w-8 h-8 animate-spin text-blue-600 mb-2" />
            <p className="text-sm text-gray-600">
              Creating AI Agent...
            </p> */}
          </motion.div>
        )}
      </AnimatePresence>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute top-2 left-2 right-2 bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded text-sm z-20"
        >
          {error}
        </motion.div>
      )}

      <div className="w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg flex flex-col items-center justify-center overflow-hidden">
        {agent ? (
          <div className="text-center w-full h-full flex flex-col">
            {/* Avatar Image */}
            <div className="flex-1 flex items-center justify-center p-4">
              {agent.presenter?.thumbnail ? (
                <Image
                  src={agent.presenter.thumbnail}
                  alt={agent.preview_name}
                  width={360}
                  height={360}
                  className="w-full h-full object-cover rounded-lg shadow-lg max-w-xs max-h-80"
                  onError={(e) => {
                    console.error("Failed to load avatar image:", agent.presenter.thumbnail);
                    // Fallback to bot icon if image fails to load
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling?.classList.remove('hidden');
                  }}
                />
              ) : null}
              {/* Fallback Bot Icon */}
              <div className={`w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center ${agent.presenter?.thumbnail ? 'hidden' : ''}`}>
                <Bot className="w-10 h-10 text-white" />
              </div>
            </div>

            {/* Agent Info */}
            <div className="bg-white/90 backdrop-blur-sm p-3 rounded-t-lg">
              <h3 className="text-lg font-semibold text-gray-800 mb-1">
                {agent.preview_name}
              </h3>
              <p className="text-sm text-gray-600">
                AI Agent Ready
              </p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center p-4">
            <div className="w-20 h-20 bg-red-300 rounded-full flex items-center justify-center mb-4 mx-auto">
              <Bot className="w-10 h-10 text-red-600" />
            </div>
            <h3 className="text-lg font-semibold text-red-600 mb-2">
              Agent Creation Failed
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Using fallback mode
            </p>
            <button
              onClick={createAgent}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          </div>
        ) : (
          <div className="text-center">
            <div className="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center mb-4 mx-auto">
              <Bot className="w-10 h-10 text-gray-500" />
            </div>
            <h3 className="text-lg font-semibold text-gray-600 mb-2">
              Initializing Agent...
            </h3>
          </div>
        )}
      </div>
    </div>
  );
};

export default DIDAgent;
