"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, CheckCircle } from "lucide-react";
import JobInfoCard from "@/components/JobInfoCard";
import QuestionsList from "@/components/QuestionsList";
import CandidateWithAgent from "@/components/CandidateWithAgent";
import LiveStreamingAvatar from "@/components/LiveStreamingAvatar";
import InterviewLayout from "@/components/InterviewLayout";
import { useInterview } from "@/context/InterviewContext";

type InterviewWithDIDProps = {
  onNext?: () => void;
  candidateName?: string;
  jobTitle?: string;
};

const InterviewWithDID: React.FC<InterviewWithDIDProps> = ({
  onNext,
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
}) => {
  const {
    questions,
    currentQuestion,
    setCurrentQuestion,
    isInterviewStarted,
    setIsInterviewStarted,
    isLiveStreamingEnabled,
    isStreamConnected,
    setIsStreamConnected,
    isStreamReady,
    setIsStreamReady,
    speakQuestion,
    speakGreeting,
  } = useInterview();

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showSubmitButton, setShowSubmitButton] = useState<boolean>(false);
  const [isWaitingForSpeech, setIsWaitingForSpeech] = useState<boolean>(false);

  const startInterview = async () => {
    setIsInterviewStarted(true);
    setIsLoading(true);
    setCurrentQuestion(1);

    // Wait for stream to be ready before speaking the first question
    if (isLiveStreamingEnabled && isStreamReady) {
      setTimeout(async () => {
        const success = await speakQuestion(0); // Speak first question (index 0)
        if (success) {
          setIsWaitingForSpeech(true);
        }
        setIsLoading(false);
      }, 1000);
    } else {
      setIsLoading(false);
    }
  };





  const handleSubmitAnswer = async () => {
    if (currentQuestion < questions.length) {
      setShowSubmitButton(false);
      setIsLoading(true);
      setIsWaitingForSpeech(false);

      const nextQuestion = currentQuestion + 1;
      setCurrentQuestion(nextQuestion);

      // Speak the next question if live streaming is enabled
      if (isLiveStreamingEnabled && isStreamReady) {
        setTimeout(async () => {
          const success = await speakQuestion(nextQuestion - 1); // Convert to 0-based index
          if (success) {
            setIsWaitingForSpeech(true);
          }
          setIsLoading(false);
        }, 1000);
      } else {
        setIsLoading(false);
      }
    } else {
      // Interview completed
      onNext?.();
    }
  };

  // Handle avatar connection ready
  const handleConnectionReady = () => {
    setIsStreamConnected(true);
    console.log("Avatar connection ready");
  };

  // Handle stream ready (can start speaking)
  const handleStreamReady = async () => {
    setIsStreamReady(true);
    console.log("Avatar stream ready");

    // Speak greeting message
    await speakGreeting(candidateName, jobTitle);
  };

  // Handle speech events
  const handleSpeechStart = () => {
    setIsWaitingForSpeech(true);
    console.log("Avatar started speaking");
  };

  const handleSpeechEnd = () => {
    setIsWaitingForSpeech(false);
    setShowSubmitButton(true);
    console.log("Avatar finished speaking");
  };

  const isInterviewComplete = currentQuestion > questions.length;

  if (!isInterviewStarted) {
    return (
      <div className="h-screen">
        <JobInfoCard />
        <InterviewLayout>
          <div className="flex flex-col md:flex-row gap-10 justify-center items-center md:items-start">
            <QuestionsList className="h-[550px]" />
            {isLiveStreamingEnabled ? (
              <LiveStreamingAvatar
                className="w-[300px] h-[300px]"
                candidateName={candidateName}
                jobTitle={jobTitle}
                onConnectionReady={handleConnectionReady}
                onStreamReady={handleStreamReady}
                onSpeechStart={handleSpeechStart}
                onSpeechEnd={handleSpeechEnd}
                autoConnect={true}
                greetingMessage={`Hello ${candidateName}! Welcome to your interview for the ${jobTitle} position. I'm your AI interviewer, and I'll be asking you a few questions today. Are you ready to begin?`}
              />
            ) : (
              <CandidateWithAgent
                className="w-[300px] h-[300px]"
                candidateName={candidateName}
                jobTitle={jobTitle}
              />
            )}
          </div>

          <div className="flex justify-center mt-10 gap-4">
            <Button
              variant="default"
              className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
              onClick={startInterview}
            >
              Start Interview
              <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
            </Button>
          </div>
          
          <div className="flex justify-center mt-5 text-2xl font-semibold text-primary">
            Ready to begin
          </div>
        </InterviewLayout>
      </div>
    );
  }

  if (isInterviewComplete) {
    return (
      <div className="h-screen">
        <JobInfoCard />
        <InterviewLayout>
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-center mb-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                Interview Completed!
              </h2>
              <p className="text-gray-600">
                Thank you for completing the interview. Your responses have been recorded.
              </p>
            </div>
            
            <Button
              variant="default"
              className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
              onClick={() => onNext?.()}
            >
              View Results
              <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
            </Button>
          </div>
        </InterviewLayout>
      </div>
    );
  }

  return (
    <div className="h-screen">
      <JobInfoCard />
      <InterviewLayout>
        <div className="flex flex-col md:flex-row gap-10 justify-center items-center md:items-start">
          <QuestionsList className="h-[550px]" />
          {isLiveStreamingEnabled ? (
            <LiveStreamingAvatar
              className="w-[300px] h-[300px]"
              candidateName={candidateName}
              jobTitle={jobTitle}
              onConnectionReady={handleConnectionReady}
              onStreamReady={handleStreamReady}
              onSpeechStart={handleSpeechStart}
              onSpeechEnd={handleSpeechEnd}
              autoConnect={true}
            />
          ) : (
            <CandidateWithAgent
              className="w-[300px] h-[300px]"
              candidateName={candidateName}
              jobTitle={jobTitle}
            />
          )}
        </div>

        <div className="flex justify-center mt-10 gap-4">
          {showSubmitButton && !isLoading && !isWaitingForSpeech ? (
            <Button
              variant="default"
              className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
              onClick={handleSubmitAnswer}
            >
              {currentQuestion < questions.length ? "Next Question" : "Finish Interview"}
              <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
            </Button>
          ) : (
            <div className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center justify-center gap-2 bg-gray-200 text-gray-500">
              {isLoading ? "Loading question..." :
               isWaitingForSpeech ? "Avatar is speaking..." :
               !isStreamReady ? "Connecting to avatar..." :
               "Listen to the question"}
            </div>
          )}
        </div>

        <div className="flex justify-center mt-5 text-2xl font-semibold text-primary">
          Question {currentQuestion} of {questions.length}
        </div>
      </InterviewLayout>
    </div>
  );
};

export default InterviewWithDID;
